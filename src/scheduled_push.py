import datetime
import json
import os
import sys
import zipfile
import logging

import requests
from apscheduler.schedulers.blocking import BlockingScheduler


logger = logging.getLogger(__name__)

curPath = os.path.abspath(os.path.dirname(__file__))
sys.path.append(os.path.split(curPath)[0])

proxies = {

}

# 1、填入webhook的key值
key = '5eaa92c2-1097-4f7b-9286-c24ca2a52fcc'
webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=" + key


# 2、选择一种样式，发消息
def qywx_message():
    header = {"Content-Type": "application/json"}

    # text样式
    # message = {
    #     "msgtype": "text",
    #     "text": {
    #         "content": "这是一条文本测试信息",
    #         "mentioned_list": ["陈星", "@陈星"],
    #     }
    # }

    # markdown样式
    # message = {
    #     "msgtype": "markdown",
    #     "markdown": {
    #         "content": "# 第2条测试信息 \n \
    #         > <font color=\"warning\">近日公告如下:</font> \n \
    #         > <font color=\"comment\">请查阅以下公告</font> \n \
    #         > <font color=\"info\">10点20分发布</font> [公告](https://www.baidu.com/) "
    #     }
    # }

    # 图文样式
    # message = {
    #     "msgtype": "news",
    #     "news": {
    #        "articles" : [
    #            {
    #                "title" : "中秋节礼品领取",
    #                "description" : "今年中秋节公司有豪礼相送",
    #                "url" : "www.qq.com",
    #                "picurl" : "http://res.mail.qq.com/node/ww/wwopenmng/images/independent/doc/test_pic_msg1.png"
    #            }
    #         ]
    #     }
    # }

    # 文件样式

    path = 'D:\\mdd\\' + (datetime.datetime.now() + datetime.timedelta(days=-1)).strftime("%Y-%m-%d")
    zip = zipfile.ZipFile(path + '.zip', 'w', zipfile.ZIP_DEFLATED)
    if os.path.exists(path):
        for name in os.listdir(path):
            zip.write(path+'\\'+name, '/'+name)
        zip.close()
        up_file_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key=" + key + "&type=file"
        filename = zip.filename    # 要上传的文件名（注意路径）

        file = {"file": open(filename, "rb")}
        res = requests.post(up_file_url, files=file, proxies=proxies) # 需要先将文件上传到腾讯的临时文件服务器
        media_id = res.json()['media_id']
        message = {
            "msgtype": "file",
            "file": {
                 "media_id": media_id
            }
        }

        message_json = json.dumps(message)
        send_message = requests.post(url=webhook, data=message_json, headers=header, proxies=proxies)
        print(send_message.text)



def check_device_status():
   print('check_device_status')
   # 检测三个设备，通过adb device或者tidevice list获取连接上的设备和已有设备udid比对，如果有，则证明插入的正常，没有断电等问题，如果不在，则打印设备已掉线


if __name__ == '__main__':
    # qywx_message()
    logger.info("start crawl app")
    scheduler = BlockingScheduler(timezone='Asia/Shanghai')
    # scheduler.add_job(qywx_message, "cron", day_of_week='*', hour='10', minute="00", misfire_grace_time=60, id='start')
    scheduler.add_job(check_device_status, "cron", second="*/10", id='check_device_status')
    scheduler.start()